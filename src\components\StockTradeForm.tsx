'use client'

import { useEffect } from 'react'
import { UseFormRegister, UseFormWatch, UseFormSetValue, FieldErrors } from 'react-hook-form'
import { z } from 'zod'
import { calculateRReturn, calculateRiskAmount } from '@/lib/database'

// Stock trade schema
export const stockTradeSchema = z.object({
  status: z.enum(['Open', 'Closed'], {
    required_error: 'Status is required',
    invalid_type_error: 'Status must be either Open or Closed'
  }),
  entry_datetime: z.string()
    .min(1, 'Entry date/time is required')
    .regex(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/, 'Invalid date/time format'),
  exit_datetime: z.string()
    .optional()
    .refine((val) => !val || /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/.test(val), 'Invalid date/time format'),
  ticker: z.string()
    .min(1, 'Ticker is required')
    .max(10, 'Ticker must be 10 characters or less')
    .regex(/^[A-Z0-9]+$/, 'Ticker must contain only letters and numbers')
    .transform(val => val.toUpperCase().trim()),
  direction: z.enum(['long', 'short'], {
    required_error: 'Direction is required',
    invalid_type_error: 'Direction must be either long or short'
  }),
  shares: z.number()
    .int('Number of shares must be a whole number')
    .positive('Number of shares must be a positive integer')
    .max(100000, 'Number of shares too large'),
  entry_price: z.number()
    .positive('Entry price must be positive')
    .min(0.01, 'Entry price must be at least $0.01')
    .max(99999.99, 'Entry price too large'),
  exit_price: z.number()
    .positive('Exit price must be positive')
    .min(0.01, 'Exit price must be at least $0.01')
    .max(99999.99, 'Exit price too large')
    .optional(),
  stop_price: z.number()
    .positive('Stop price must be positive')
    .min(0.01, 'Stop price must be at least $0.01')
    .max(99999.99, 'Stop price too large')
    .optional(),
  mfe_price: z.number()
    .positive('MFE price must be positive')
    .min(0.01, 'MFE price must be at least $0.01')
    .max(99999.99, 'MFE price too large')
    .optional(),
}).refine((data) => {
  // For Closed trades, require entry_price, exit_price, and stop_price
  if (data.status === 'Closed') {
    return data.entry_price && data.exit_price && data.stop_price;
  }
  // For Open trades, require entry_price and stop_price
  if (data.status === 'Open') {
    return data.entry_price && data.stop_price;
  }
  return true;
}, {
  message: 'Closed trades require entry price, exit price, and stop price. Open trades require entry price and stop price.',
  path: ['status']
})

export type StockTradeFormData = z.infer<typeof stockTradeSchema>

interface StockTradeFormProps {
  register: UseFormRegister<StockTradeFormData>
  watch: UseFormWatch<StockTradeFormData>
  setValue: UseFormSetValue<StockTradeFormData>
  errors: FieldErrors<StockTradeFormData>
}

export default function StockTradeForm({ register, watch, setValue, errors }: StockTradeFormProps) {
  const exitDateTime = watch('exit_datetime')
  const entryDateTime = watch('entry_datetime')
  const direction = watch('direction')
  const entryPrice = watch('entry_price')
  const exitPrice = watch('exit_price')
  const stopPrice = watch('stop_price')
  const shares = watch('shares')
  const mfePrice = watch('mfe_price')

  // Calculate R-return when we have all required values
  const rReturn = entryPrice && exitPrice && stopPrice && direction
    ? calculateRReturn(entryPrice, exitPrice, stopPrice, direction)
    : null

  // Calculate risk amount when we have all required values
  const riskAmount = entryPrice && stopPrice && shares && direction
    ? calculateRiskAmount(entryPrice, stopPrice, shares, direction)
    : null

  // Calculate MFE R-return when we have all required values
  const mfeRReturn = entryPrice && mfePrice && stopPrice && direction
    ? calculateRReturn(entryPrice, mfePrice, stopPrice, direction)
    : null

  // Format price to 2 decimal places
  const formatPrice = (value: string | number) => {
    const numValue = typeof value === 'string' ? parseFloat(value) : value
    if (isNaN(numValue) || numValue === 0) return '0.00'
    return numValue.toFixed(2)
  }

  // Handle price field blur to format to 2 decimal places
  const handlePriceBlur = (fieldName: 'entry_price' | 'exit_price' | 'stop_price' | 'mfe_price', value: string) => {
    if (value && value.trim() !== '' && !isNaN(parseFloat(value))) {
      const formattedValue = formatPrice(value)
      setValue(fieldName, parseFloat(formattedValue))
      // Update the actual input element to show formatted value
      const input = document.querySelector(`input[name="${fieldName}"]`) as HTMLInputElement
      if (input) {
        input.value = formattedValue
      }
    } else if (value === '' || value.trim() === '') {
      // Clear the field if empty
      setValue(fieldName, undefined)
      const input = document.querySelector(`input[name="${fieldName}"]`) as HTMLInputElement
      if (input) {
        input.value = ''
      }
    }
  }

  // Auto-populate exit datetime with entry date when entry_datetime changes
  useEffect(() => {
    if (entryDateTime) {
      // Auto-populate exit datetime with same date/time as entry (user can edit)
      setValue('exit_datetime', entryDateTime)
    }
  }, [entryDateTime, setValue])

  // Handle price field focus to allow easy editing
  const handlePriceFocus = (fieldName: 'entry_price' | 'exit_price' | 'stop_price' | 'mfe_price') => {
    const input = document.querySelector(`input[name="${fieldName}"]`) as HTMLInputElement
    if (input) {
      // Select all text for easy replacement
      input.select()
    }
  }

  return (
    <div className="space-y-4">
      {/* Date/Time Fields */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {/* Entry Date/Time */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Entry Date/Time *
          </label>
          <input
            type="datetime-local"
            {...register('entry_datetime')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {errors.entry_datetime && (
            <p className="text-red-500 text-sm mt-1">{errors.entry_datetime.message}</p>
          )}
        </div>

        {/* Exit Date/Time */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Exit Date/Time
          </label>
          <input
            type="datetime-local"
            {...register('exit_datetime')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {errors.exit_datetime && (
            <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.exit_datetime.message}</p>
          )}
        </div>
      </div>

      {/* Status and Ticker */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {/* Status */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Status *
          </label>
          <select
            {...register('status')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select status</option>
            <option value="Open">Open</option>
            <option value="Closed">Closed</option>
          </select>
          {errors.status && (
            <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.status.message}</p>
          )}
        </div>

        {/* Ticker */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Ticker *
          </label>
          <input
            type="text"
            placeholder="e.g., AAPL, TSLA, MSFT"
            {...register('ticker')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {errors.ticker && (
            <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.ticker.message}</p>
          )}
        </div>
      </div>

      {/* Direction and Shares */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {/* Direction */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Direction *
          </label>
          <select
            {...register('direction')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select direction</option>
            <option value="long">Long (Buy)</option>
            <option value="short">Short (Sell)</option>
          </select>
          {errors.direction && (
            <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.direction.message}</p>
          )}
        </div>

        {/* Number of Shares */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Number of Shares *
          </label>
          <input
            type="number"
            min="1"
            {...register('shares', { valueAsNumber: true })}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {errors.shares && (
            <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.shares.message}</p>
          )}
        </div>
      </div>

      {/* Price Fields */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        {/* Entry Price */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Entry Price *
          </label>
          <input
            type="text"
            inputMode="decimal"
            placeholder="0.00"
            {...register('entry_price', {
              valueAsNumber: true,
              validate: (value) => {
                if (!value && value !== 0) return 'Entry price is required'
                const num = typeof value === 'number' ? value : parseFloat(String(value))
                if (isNaN(num)) return 'Please enter a valid price'
                if (num < 0.01) return 'Price must be at least $0.01'
                if (num > 99999.99) return 'Price too large'
                return true
              }
            })}
            onBlur={(e) => handlePriceBlur('entry_price', e.target.value)}
            onFocus={() => handlePriceFocus('entry_price')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {errors.entry_price && (
            <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.entry_price.message}</p>
          )}
        </div>

        {/* Exit Price */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Exit Price {exitDateTime ? '*' : ''}
          </label>
          <input
            type="text"
            inputMode="decimal"
            placeholder="0.00"
            {...register('exit_price', {
              valueAsNumber: true,
              validate: (value) => {
                if (!value) return true // Optional field
                const num = typeof value === 'number' ? value : parseFloat(String(value))
                if (isNaN(num)) return 'Please enter a valid price'
                if (num < 0.01) return 'Price must be at least $0.01'
                if (num > 99999.99) return 'Price too large'
                return true
              }
            })}
            onBlur={(e) => handlePriceBlur('exit_price', e.target.value)}
            onFocus={() => handlePriceFocus('exit_price')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {errors.exit_price && (
            <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.exit_price.message}</p>
          )}
        </div>

        {/* Stop Price */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Stop Price
          </label>
          <input
            type="text"
            inputMode="decimal"
            placeholder="0.00"
            {...register('stop_price', {
              valueAsNumber: true,
              validate: (value) => {
                if (!value) return true // Optional field
                const num = typeof value === 'number' ? value : parseFloat(String(value))
                if (isNaN(num)) return 'Please enter a valid price'
                if (num < 0.01) return 'Price must be at least $0.01'
                if (num > 99999.99) return 'Price too large'
                return true
              }
            })}
            onBlur={(e) => handlePriceBlur('stop_price', e.target.value)}
            onFocus={() => handlePriceFocus('stop_price')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {errors.stop_price && (
            <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.stop_price.message}</p>
          )}
        </div>
      </div>

      {/* MFE Price Field */}
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Maximum Favorable Excursion (MFE) Price
        </label>
        <input
          type="text"
          inputMode="decimal"
          placeholder="0.00"
          {...register('mfe_price', {
            valueAsNumber: true,
            validate: (value) => {
              if (!value) return true // Optional field
              const num = typeof value === 'number' ? value : parseFloat(String(value))
              if (isNaN(num)) return 'Please enter a valid price'
              if (num < 0.01) return 'Price must be at least $0.01'
              if (num > 99999.99) return 'Price too large'
              return true
            }
          })}
          onBlur={(e) => handlePriceBlur('mfe_price', e.target.value)}
          onFocus={() => handlePriceFocus('mfe_price')}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
        />
        {errors.mfe_price && (
          <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.mfe_price.message}</p>
        )}
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          The highest favorable price reached during the trade (optional)
        </p>
      </div>

      {/* Calculated Fields Display */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        {/* Risk Amount */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Risk Amount
          </label>
          <input
            type="text"
            value={riskAmount !== null ? `$${riskAmount.toFixed(2)}` : 'N/A'}
            readOnly
            title="Total risk amount based on entry price, stop price, and number of shares"
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-600 text-gray-600 dark:text-gray-300"
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Total risk amount (requires stop price and shares)
          </p>
        </div>

        {/* R-Return */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            R-Return
          </label>
          <input
            type="text"
            value={rReturn !== null ? `${rReturn.toFixed(2)}R` : 'N/A'}
            readOnly
            title="Risk-reward ratio based on entry, exit, and stop prices"
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-600 text-gray-600 dark:text-gray-300"
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Risk-reward ratio (requires stop price)
          </p>
        </div>

        {/* MFE R-Return */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            MFE R-Return
          </label>
          <input
            type="text"
            value={mfeRReturn !== null ? `${mfeRReturn.toFixed(2)}R` : 'N/A'}
            readOnly
            title="Maximum Favorable Excursion R-return based on entry, MFE, and stop prices"
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-600 text-gray-600 dark:text-gray-300"
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            MFE risk-reward ratio (requires MFE and stop price)
          </p>
        </div>
      </div>
    </div>
  )
}
