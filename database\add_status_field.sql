-- Add status field to trades table
-- This SQL command adds the new status field without erasing existing trades

ALTER TABLE trades 
ADD COLUMN status VARCHAR(6) NOT NULL DEFAULT 'Closed' 
CHECK (status IN ('Open', 'Closed'));

-- Update existing trades to have 'Closed' status if they have an exit_price, 'Open' if they don't
UPDATE trades 
SET status = CASE 
    WHEN exit_price IS NOT NULL THEN 'Closed'
    ELSE 'Open'
END;

-- Add comment to document the new field
COMMENT ON COLUMN trades.status IS 'Trade status: Open (position still active) or Closed (position has been exited)';
